2025-08-03 19:25:57.172 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 2次/秒
2025-08-03 19:25:57 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-03 19:25:57 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-03 19:25:57 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-03 19:25:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-03 19:25:57.172 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-03 19:25:57 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-03 19:25:57 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-03 19:25:57 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-03 19:25:57 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-03 19:25:57.793 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:25:58.286 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:25:58.287 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:25:58.785 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:25:58.785 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:25:58 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ADA-USDT
2025-08-03 19:25:58.785 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:25:59.291 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:25:59.291 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:25:59.787 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:25:59.787 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:25:59 [DEBUG] [OKXExchange] OKX预设置杠杆成功: DOGE-USDT
2025-08-03 19:25:59.788 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:26:00.289 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:26:00.289 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:26:00.781 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:26:00.781 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:26:00 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SOL-USDT
2025-08-03 19:26:00.781 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:26:01.286 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:26:01.286 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:26:01.789 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:26:01.789 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:26:01 [DEBUG] [OKXExchange] OKX预设置杠杆成功: AVAX-USDT
2025-08-03 19:26:01 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-03 19:26:02.281 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-03 19:26:03.476 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-03 19:26:04.983 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-03 19:26:06.498 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-03 19:26:27.567 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:26:27.568 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:26:28.565 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:26:28.565 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:26:29.556 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:26:29.556 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:26:30.561 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:26:30.561 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:26:31.560 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:26:31.560 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:26:32.559 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:26:32.559 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:27:17.061 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-03 19:27:22.268 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-03 19:27:23.781 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-03 19:27:24.271 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-03 19:27:24.275 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-03 19:27:24.279 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-03 19:27:26.348 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-03 19:27:27.780 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:27:27.781 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:27:27.781 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:27:27.782 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:27:27.782 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:27:27.782 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SHIB-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:27:27.782 [INFO] [exchanges.okx_exchange] OKX设置杠杆: BNB-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 19:27:27.860 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:27:27.860 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:27:28.348 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:27:28.348 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:27:28.357 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:27:28.357 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:27:28.373 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:27:28.373 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:27:28.375 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 19:27:28.375 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 19:27:28.375 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 19:27:28.375 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 19:27:28.375 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 19:27:28.376 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 19:27:28.376 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 19:27:28.376 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 19:27:28 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 19:27:28.384 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:27:28.385 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:27:28.390 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:27:28.390 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:27:28.392 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 19:27:28.392 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 19:27:28.392 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 19:27:28.393 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 19:27:28.393 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 19:27:28.393 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 19:27:28.393 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 19:27:28.393 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 19:27:28 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 19:27:28.861 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:27:28.862 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:27:28.864 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:27:28.864 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:27:28.865 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:27:28.865 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:27:28.871 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:27:28.871 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:27:30.466 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:27:30.466 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:27:30.959 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 19:27:30.959 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 19:27:30.969 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:27:30.969 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:27:31.456 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 19:27:31.456 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 19:27:48.639 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-03 19:27:51.638 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-03 19:27:53.141 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-03 19:27:54.641 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-03 19:27:59.309 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-03 19:28:00.801 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-03 19:28:02.905 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-03 19:28:03.802 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
