#!/usr/bin/env python3
"""
原始错误重现测试脚本
2025-08-03

目标：重现用户报告的原始错误，验证修复后是否彻底解决
"""

import sys
import os
import asyncio
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_original_error_scenarios():
    """测试原始错误场景"""
    print("🔍 开始重现原始错误场景测试...")
    
    test_results = {
        "original_error_fixed": False,
        "tests": [],
        "issues": []
    }
    
    try:
        # 1. 重现原始错误：WebSocket管理器初始化
        test_ws_init = {
            "name": "WebSocket管理器初始化（原始错误场景）",
            "passed": False,
            "details": "",
            "original_error": "'WebSocketManager' object has no attribute 'connection_pool_manager'"
        }
        
        try:
            from websocket.ws_manager import WebSocketManager
            ws_manager = WebSocketManager()
            
            # 尝试访问connection_pool_manager（原始错误点）
            pool_manager = ws_manager.connection_pool_manager
            
            test_ws_init["passed"] = pool_manager is not None
            test_ws_init["details"] = f"成功访问connection_pool_manager: {type(pool_manager)}"
        except AttributeError as e:
            if "connection_pool_manager" in str(e):
                test_ws_init["details"] = f"原始错误仍然存在: {e}"
                test_results["issues"].append({
                    "type": "CRITICAL",
                    "description": "原始AttributeError错误仍然存在",
                    "error": str(e)
                })
            else:
                test_ws_init["details"] = f"其他AttributeError: {e}"
        except Exception as e:
            test_ws_init["details"] = f"其他错误: {e}"
        
        test_results["tests"].append(test_ws_init)
        
        # 2. 测试原始错误中的具体代码行
        test_specific_lines = {
            "name": "原始错误具体代码行测试",
            "passed": False,
            "details": ""
        }
        
        try:
            if test_ws_init["passed"]:
                # 模拟第231行和第274行的调用
                pool_manager = ws_manager.connection_pool_manager
                create_connection_method = getattr(pool_manager, 'create_connection', None)
                
                # 模拟第625行、673行、730行的调用
                start_monitoring_method = getattr(pool_manager, 'start_monitoring', None)
                stop_monitoring_method = getattr(pool_manager, 'stop_monitoring', None)
                
                methods_available = [
                    create_connection_method is not None and callable(create_connection_method),
                    start_monitoring_method is not None and callable(start_monitoring_method),
                    stop_monitoring_method is not None and callable(stop_monitoring_method)
                ]
                
                test_specific_lines["passed"] = all(methods_available)
                test_specific_lines["details"] = f"关键方法可用性: create_connection={methods_available[0]}, start_monitoring={methods_available[1]}, stop_monitoring={methods_available[2]}"
            else:
                test_specific_lines["details"] = "无法测试，connection_pool_manager不可访问"
        except Exception as e:
            test_specific_lines["details"] = f"具体代码行测试失败: {e}"
        
        test_results["tests"].append(test_specific_lines)
        
        # 3. 测试三个交易所的WebSocket客户端初始化
        test_exchanges = {
            "name": "三个交易所WebSocket客户端初始化",
            "passed": False,
            "details": ""
        }
        
        try:
            # 测试Gate.io
            from websocket.gate_ws_client import GateWebSocketClient
            gate_client = GateWebSocketClient(symbols=["BTC-USDT"], is_spot=True)
            
            # 测试Bybit
            from websocket.bybit_ws_client import BybitWebSocketClient  
            bybit_client = BybitWebSocketClient(symbols=["BTC-USDT"], is_spot=True)
            
            # 测试OKX
            from websocket.okx_ws_client import OKXWebSocketClient
            okx_client = OKXWebSocketClient(symbols=["BTC-USDT"], is_spot=True)
            
            test_exchanges["passed"] = True
            test_exchanges["details"] = "三个交易所WebSocket客户端创建成功"
        except Exception as e:
            test_exchanges["details"] = f"交易所客户端创建失败: {e}"
            if "connection_pool_manager" in str(e):
                test_results["issues"].append({
                    "type": "CRITICAL",
                    "description": "交易所客户端仍然出现connection_pool_manager错误",
                    "error": str(e)
                })
        
        test_results["tests"].append(test_exchanges)
        
        # 4. 测试WebSocket管理器的完整初始化流程
        test_full_init = {
            "name": "WebSocket管理器完整初始化流程",
            "passed": False,
            "details": ""
        }
        
        try:
            ws_manager = WebSocketManager()
            ws_manager.add_symbols(["BTC-USDT", "ETH-USDT"])
            
            # 检查是否可以调用关键方法而不出现connection_pool_manager错误
            pool_manager = ws_manager.connection_pool_manager
            
            # 检查连接池管理器是否正确初始化
            if hasattr(pool_manager, 'create_connection'):
                test_full_init["passed"] = True
                test_full_init["details"] = "完整初始化流程成功，connection_pool_manager正常工作"
            else:
                test_full_init["details"] = "连接池管理器缺少关键方法"
        except AttributeError as e:
            if "connection_pool_manager" in str(e):
                test_full_init["details"] = f"完整初始化仍然出现原始错误: {e}"
            else:
                test_full_init["details"] = f"其他AttributeError: {e}"
        except Exception as e:
            test_full_init["details"] = f"完整初始化失败: {e}"
        
        test_results["tests"].append(test_full_init)
        
        # 5. 计算修复成功率
        passed_tests = sum(1 for test in test_results["tests"] if test["passed"])
        total_tests = len(test_results["tests"])
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        test_results["original_error_fixed"] = success_rate >= 75  # 75%以上认为原始错误已修复
        test_results["success_rate"] = success_rate
        test_results["passed_tests"] = passed_tests
        test_results["total_tests"] = total_tests
        
        return test_results
        
    except Exception as e:
        test_results["issues"].append({
            "type": "CRITICAL",
            "description": f"原始错误重现测试失败: {e}",
            "error": str(e)
        })
        return test_results

def print_test_results(results):
    """打印测试结果"""
    print("\n📊 原始错误重现测试结果:")
    print("=" * 60)
    
    # 总体结果
    if results["original_error_fixed"]:
        print(f"✅ 原始错误已修复！成功率: {results['success_rate']:.1f}% ({results['passed_tests']}/{results['total_tests']})")
    else:
        print(f"❌ 原始错误未完全修复！成功率: {results['success_rate']:.1f}% ({results['passed_tests']}/{results['total_tests']})")
    
    print("\n🧪 详细测试结果:")
    for i, test in enumerate(results["tests"], 1):
        status = "✅ 通过" if test["passed"] else "❌ 失败"
        print(f"   {i}. {test['name']}: {status}")
        print(f"      详情: {test['details']}")
        if 'original_error' in test:
            print(f"      原始错误: {test['original_error']}")
    
    # 问题列表
    if results["issues"]:
        print(f"\n🚨 发现 {len(results['issues'])} 个问题:")
        for i, issue in enumerate(results["issues"], 1):
            print(f"   {i}. {issue['type']}: {issue['description']}")
            if 'error' in issue:
                print(f"      错误: {issue['error']}")

async def main():
    """主函数"""
    print("🔧 原始错误重现测试")
    print("=" * 80)
    print("目标：验证用户报告的原始错误是否已彻底修复")
    print("原始错误：'WebSocketManager' object has no attribute 'connection_pool_manager'")
    print()
    
    # 执行测试
    results = await test_original_error_scenarios()
    
    # 打印结果
    print_test_results(results)
    
    # 保存结果
    timestamp = int(time.time())
    result_file = project_root / "diagnostic_scripts" / f"original_error_reproduction_test_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📊 测试结果已保存到: {result_file}")
    
    # 总结
    if results["original_error_fixed"]:
        print("\n🎉 原始错误已彻底修复！用户报告的问题已解决")
        print("✅ WebSocket管理器connection_pool_manager属性问题修复成功")
        print("✅ 所有三个交易所的WebSocket客户端可以正常初始化")
    else:
        print("\n⚠️ 原始错误未完全修复！需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
